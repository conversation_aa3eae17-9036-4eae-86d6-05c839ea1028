<script lang="ts">
import type { Character } from "$lib/types/Character";
import { T } from "@threlte/core";
import CharacterReference from "./CharacterReference.svelte";
import {CubicBezierCurve3, Group, Texture} from "three";
import CurveEditor from "./CurveEditor.svelte";
import type { Bezier } from "$/lib/types/Bezier.svelte";
    import { Spring } from "svelte/motion";

let {
    character,
    onReferenceCurveChange,
    onTextureChange,
}: {
    character: Character,
    onReferenceCurveChange?: (referenceCurve: Bezier[]) => void,
    onTextureChange?: (texture: Texture) => void,
} = $props();

const scaleFac = new Spring(character.referenceCurve.targetScaleFac);
$effect(() => {
    scaleFac.target = character.referenceCurve.targetScaleFac;
});

let bottomLeftLocalCoords = $state({x: 0, y: 0});

let innerGroupRef: Group = $state()!;
const innerGroupMatrix = $derived(innerGroupRef.matrixWorld.clone().invert());
</script>

<T.Group
    scale={scaleFac.current}
>
    <T.Group
        position={[bottomLeftLocalCoords.x, 0, 0]}
        bind:ref={innerGroupRef}
    >
        <CharacterReference
            {character}
            onBottomLeftLocalCoordsChange={value => bottomLeftLocalCoords = value}
            {onTextureChange}
        />

        <CurveEditor
            curve={character.referenceCurve.segments}
            onCurveChange={onReferenceCurveChange}
            meshLineScaleFac={character.referenceCurve.targetScaleFac}
            groupMatrix={innerGroupMatrix}
        />
    </T.Group>
</T.Group>